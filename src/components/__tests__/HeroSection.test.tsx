import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import HeroSection from '../HeroSection';

describe('HeroSection', () => {
  test('renders with red gradient background', () => {
    render(<HeroSection />);

    // Get the section element
    const sectionElement = document.querySelector('section');

    // Check if the section has the gradient background classes
    expect(sectionElement).toHaveClass('bg-gradient-to-br', 'from-firefly-red', 'to-[#8B0000]');

    // Verify the burger image is present with correct path
    const burgerImage = document.querySelector('img[src="/burger-02.png"]');
    expect(burgerImage).toBeInTheDocument();
    expect(burgerImage).toHaveAttribute('alt', 'Signature FireFly Burger');

    // Verify the content is present
    expect(screen.getByText('ABOVE ALL BURGERS')).toBeInTheDocument();
    expect(screen.getByText('Premium gourmet burgers crafted to ignite your taste buds and satisfy your cravings.')).toBeInTheDocument();
  });
});
