import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SearchSection from '../SearchSection';

describe('SearchSection', () => {
  test('renders search functionality with CTA', () => {
    render(<SearchSection />);
    
    // Get the section element
    const sectionElement = document.querySelector('section');
    
    // Check if the section has the white background
    expect(sectionElement).toHaveClass('bg-white');
    
    // Verify the CTA content is present
    expect(screen.getByText('Find Your Perfect Burger')).toBeInTheDocument();
    expect(screen.getByText('Search our menu and discover your next favorite meal')).toBeInTheDocument();
    
    // Verify the search box is present
    const searchInput = screen.getByPlaceholderText('Search for food items...');
    expect(searchInput).toBeInTheDocument();
    expect(searchInput).toHaveAttribute('type', 'text');
    
    // Verify search icon is present
    const searchIcon = document.querySelector('svg');
    expect(searchIcon).toBeInTheDocument();
  });
});
