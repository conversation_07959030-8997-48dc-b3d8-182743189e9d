import { useState } from "react";
import { Search } from "lucide-react";

const SearchSection = () => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // TODO: Implement search functionality
      console.log("Searching for:", searchQuery);
    }
  };

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Call-to-Action */}
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-bold text-firefly-black mb-4">
            Find Your Perfect Burger
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Search our menu and discover your next favorite meal
          </p>
        </div>
        
        {/* Search Box */}
        <div className="flex justify-center">
          <div className="w-full max-w-2xl">
            <form onSubmit={handleSearch} className="relative">
              <div className="relative">
                <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
                <input
                  type="text"
                  placeholder="Search for food items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-16 pr-6 py-5 text-lg border-2 border-gray-200 rounded-xl text-gray-800 placeholder-gray-400 focus:outline-none focus:border-firefly-red focus:ring-2 focus:ring-firefly-red/20 transition-all duration-300 shadow-lg hover:shadow-xl"
                />
              </div>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SearchSection;
