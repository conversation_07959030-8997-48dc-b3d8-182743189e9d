// No imports needed for this simplified hero section
const HeroSection = () => {
  // CHANGED: Updated background from solid color to gradient to match card styling
  return <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-firefly-red to-[#8B0000] overflow-hidden">
      {/* Background overlay */}
      {/* CHANGED: Updated gradient colors from black to red to match the new background */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-firefly-red/50 to-firefly-red z-10"></div>
      
      {/* 
      * REMOVED: Hero background image 
      * As part of the redesign, we're removing the background image to use a solid color instead
      */}
      
      {/* Red glow effect */}
      <div className="absolute inset-0 bg-gradient-to-tr from-firefly-red/20 via-transparent to-transparent z-5"></div>
      
      {/* Content */}
      <div className="container mx-auto px-4 z-20">
        {/* Two-column layout for desktop, stacked for mobile */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-8">
          {/* Hero content - Centered with stacked text design */}
          <div className="w-full flex flex-col items-center justify-center relative pt-24 md:pt-48 lg:pt-64">
            {/* IS BACK style stacked text with burger integrated */}
            <div className="relative text-center mb-10">
              <h1 className="font-extrabold text-firefly-cream tracking-tighter leading-none">
                <div className="flex flex-col items-center justify-center leading-[0.85] tracking-tight">
                  {/* First line */}
                  <div className="whitespace-nowrap text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl flex items-center justify-center">
                    ABOVE ALL BURGERS
                  </div>
                  {/* Second line */}
                  <div className="whitespace-nowrap text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl flex items-center justify-center">
                    ABOVE ALL BURGERS
                  </div>
                  {/* Third line */}
                  <div className="whitespace-nowrap text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl flex items-center justify-center">
                    ABOVE ALL BURGERS
                  </div>
                </div>
              </h1>
              
              {/* Burger image positioned absolutely */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <img 
                  src="/burger-02.png" 
                  alt="Signature FireFly Burger" 
                  className="object-cover drop-shadow-2xl" 
                />
                {/* Add a subtle glow effect behind the burger */}
                <div className="absolute -inset-8 bg-firefly-red/30 rounded-full filter blur-xl -z-10"></div>
              </div>
            </div>
            
            <p className="text-xl md:text-3xl mb-10 text-white/90 max-w-2xl text-center">
              Premium gourmet burgers crafted to ignite your taste buds and satisfy your cravings.
            </p>
          </div>
        </div>
      </div>
    </section>;
};
export default HeroSection;