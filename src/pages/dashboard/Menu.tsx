import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Eye,
  Trash2,
  UtensilsCrossed,
  DollarSign,
  Clock,
  Star,
  Tags,
} from "lucide-react";

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  status: "available" | "unavailable" | "seasonal";
  preparationTime: number;
  rating: number;
  isPopular: boolean;
  allergens: string[];
  lastUpdated: string;
}

const Menu = () => {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock data loading
    const loadMenuItems = async () => {
      await new Promise(resolve => setTimeout(resolve, 800));

      setMenuItems([
        {
          id: "ITEM-001",
          name: "Classic Firefly Burger",
          description: "Our signature beef patty with lettuce, tomato, onion, and special sauce",
          price: 12.99,
          category: "Beef Burgers",
          image: "/burger.png",
          status: "available",
          preparationTime: 15,
          rating: 4.8,
          isPopular: true,
          allergens: ["gluten", "dairy"],
          lastUpdated: "2024-01-15T10:30:00Z",
        },
        {
          id: "ITEM-002",
          name: "Spicy Jalapeño Burger",
          description: "Beef patty with jalapeños, pepper jack cheese, and chipotle mayo",
          price: 14.99,
          category: "Beef Burgers",
          image: "/burger-02.png",
          status: "available",
          preparationTime: 18,
          rating: 4.6,
          isPopular: true,
          allergens: ["gluten", "dairy"],
          lastUpdated: "2024-01-14T15:20:00Z",
        },
        {
          id: "ITEM-003",
          name: "Crispy Chicken Deluxe",
          description: "Crispy chicken breast with bacon, avocado, and ranch dressing",
          price: 13.99,
          category: "Chicken Burgers",
          image: "/burger-top.png",
          status: "available",
          preparationTime: 20,
          rating: 4.5,
          isPopular: false,
          allergens: ["gluten", "dairy", "eggs"],
          lastUpdated: "2024-01-13T09:15:00Z",
        },
        {
          id: "ITEM-004",
          name: "Veggie Garden Burger",
          description: "Plant-based patty with fresh vegetables and herb aioli",
          price: 11.99,
          category: "Veggie Burgers",
          image: "/burger.png",
          status: "available",
          preparationTime: 12,
          rating: 4.3,
          isPopular: false,
          allergens: ["gluten"],
          lastUpdated: "2024-01-15T14:45:00Z",
        },
        {
          id: "ITEM-005",
          name: "Loaded Cheese Fries",
          description: "Crispy fries topped with melted cheese, bacon bits, and green onions",
          price: 8.99,
          category: "Loaded Fries",
          image: "/burger-02.png",
          status: "available",
          preparationTime: 10,
          rating: 4.7,
          isPopular: true,
          allergens: ["dairy"],
          lastUpdated: "2024-01-10T11:30:00Z",
        },
        {
          id: "ITEM-006",
          name: "Chocolate Milkshake",
          description: "Rich chocolate milkshake topped with whipped cream",
          price: 5.99,
          category: "Drinks & Shakes",
          image: "/burger-top.png",
          status: "seasonal",
          preparationTime: 5,
          rating: 4.4,
          isPopular: false,
          allergens: ["dairy"],
          lastUpdated: "2024-01-15T08:20:00Z",
        },
        {
          id: "ITEM-007",
          name: "BBQ Bacon Burger",
          description: "Beef patty with BBQ sauce, crispy bacon, and onion rings",
          price: 15.99,
          category: "Beef Burgers",
          image: "/burger.png",
          status: "unavailable",
          preparationTime: 22,
          rating: 4.9,
          isPopular: true,
          allergens: ["gluten", "dairy"],
          lastUpdated: "2024-01-12T16:45:00Z",
        },
        {
          id: "ITEM-008",
          name: "Sweet Potato Fries",
          description: "Crispy sweet potato fries with honey mustard dipping sauce",
          price: 6.99,
          category: "Sides",
          image: "/burger-02.png",
          status: "available",
          preparationTime: 8,
          rating: 4.2,
          isPopular: false,
          allergens: [],
          lastUpdated: "2024-01-14T12:30:00Z",
        },
      ]);

      setIsLoading(false);
    };

    loadMenuItems();
  }, []);

  const categories = ["all", "Beef Burgers", "Chicken Burgers", "Veggie Burgers", "Loaded Fries", "Drinks & Shakes", "Sides"];

  const filteredItems = menuItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter;
    const matchesStatus = statusFilter === "all" || item.status === statusFilter;
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      available: "bg-green-100 text-green-800",
      unavailable: "bg-red-100 text-red-800",
      seasonal: "bg-yellow-100 text-yellow-800",
    };

    return (
      <Badge className={variants[status as keyof typeof variants] || variants.available}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price);
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-80 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Menu Management</h1>
            <p className="text-gray-600">Manage your restaurant menu items and categories</p>
          </div>
          <div className="flex space-x-2">
            <Link to="/dashboard/menu/categories">
              <Button variant="outline">
                <Tags className="w-4 h-4 mr-2" />
                Categories
              </Button>
            </Link>
            <Link to="/dashboard/menu/new">
              <Button className="bg-firefly-red hover:bg-red-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Item
              </Button>
            </Link>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search menu items..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Filter by category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category === "all" ? "All Categories" : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="available">Available</SelectItem>
              <SelectItem value="unavailable">Unavailable</SelectItem>
              <SelectItem value="seasonal">Seasonal</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Menu Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <img 
                      src={item.image} 
                      alt={item.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <CardTitle className="text-lg">{item.name}</CardTitle>
                        {item.isPopular && (
                          <Badge className="bg-firefly-red text-white">
                            <Star className="w-3 h-3 mr-1" />
                            Popular
                          </Badge>
                        )}
                      </div>
                      <CardDescription className="text-sm mt-1">
                        {item.category}
                      </CardDescription>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Item
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center justify-between mt-2">
                  {getStatusBadge(item.status)}
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Star className="w-4 h-4 text-yellow-500" />
                    <span>{item.rating}</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600 line-clamp-2">{item.description}</p>
                
                {/* Stats */}
                <div className="grid grid-cols-2 gap-4 pt-2 border-t">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="w-4 h-4 text-green-500" />
                    <span className="font-semibold text-lg">{formatPrice(item.price)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-500" />
                    <span className="text-sm">{item.preparationTime} min</span>
                  </div>
                </div>

                {/* Allergens */}
                {item.allergens.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {item.allergens.map(allergen => (
                      <Badge key={allergen} variant="outline" className="text-xs">
                        {allergen}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="w-4 h-4 mr-2" />
                    View
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredItems.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <UtensilsCrossed className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || categoryFilter !== "all" || statusFilter !== "all" 
                ? "No menu items found" 
                : "No menu items yet"
              }
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || categoryFilter !== "all" || statusFilter !== "all"
                ? "Try adjusting your search or filter criteria" 
                : "Get started by adding your first menu item"
              }
            </p>
            {!searchTerm && categoryFilter === "all" && statusFilter === "all" && (
              <Link to="/dashboard/menu/new">
                <Button className="bg-firefly-red hover:bg-red-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Menu Item
                </Button>
              </Link>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Menu;
