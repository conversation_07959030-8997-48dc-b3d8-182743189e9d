import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Globe,
  Calendar,
  BarChart3,
} from "lucide-react";

interface AnalyticsData {
  revenue: {
    current: number;
    previous: number;
    change: number;
  };
  orders: {
    current: number;
    previous: number;
    change: number;
  };
  customers: {
    current: number;
    previous: number;
    change: number;
  };
  websites: {
    current: number;
    previous: number;
    change: number;
  };
  topWebsites: Array<{
    name: string;
    revenue: number;
    orders: number;
    growth: number;
  }>;
  recentMetrics: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
}

const Analytics = () => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState("30d");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock data loading
    const loadAnalytics = async () => {
      await new Promise(resolve => setTimeout(resolve, 1200));

      setAnalytics({
        revenue: {
          current: 45230,
          previous: 38950,
          change: 16.1,
        },
        orders: {
          current: 1847,
          previous: 1623,
          change: 13.8,
        },
        customers: {
          current: 324,
          previous: 298,
          change: 8.7,
        },
        websites: {
          current: 12,
          previous: 10,
          change: 20.0,
        },
        topWebsites: [
          {
            name: "Burger Palace",
            revenue: 15670,
            orders: 298,
            growth: 24.5,
          },
          {
            name: "Downtown Burgers",
            revenue: 12450,
            orders: 234,
            growth: 18.2,
          },
          {
            name: "Gourmet Bites",
            revenue: 9870,
            orders: 189,
            growth: 12.8,
          },
          {
            name: "Healthy Bites",
            revenue: 8920,
            orders: 145,
            growth: 15.3,
          },
          {
            name: "Quick Eats",
            revenue: 7650,
            orders: 156,
            growth: -5.2,
          },
        ],
        recentMetrics: [
          { date: "Jan 1", revenue: 1200, orders: 45 },
          { date: "Jan 2", revenue: 1450, orders: 52 },
          { date: "Jan 3", revenue: 1320, orders: 48 },
          { date: "Jan 4", revenue: 1680, orders: 61 },
          { date: "Jan 5", revenue: 1890, orders: 67 },
          { date: "Jan 6", revenue: 2100, orders: 74 },
          { date: "Jan 7", revenue: 1950, orders: 69 },
        ],
      });

      setIsLoading(false);
    };

    loadAnalytics();
  }, [timeRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? "+" : "";
    return `${sign}${value.toFixed(1)}%`;
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? (
      <TrendingUp className="w-4 h-4 text-green-500" />
    ) : (
      <TrendingDown className="w-4 h-4 text-red-500" />
    );
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? "text-green-600" : "text-red-600";
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-96 bg-gray-200 rounded"></div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!analytics) return null;

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
            <p className="text-gray-600">Track your business performance and growth</p>
          </div>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-48">
              <Calendar className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(analytics.revenue.current)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(analytics.revenue.change)}
                <span className={`ml-1 ${getChangeColor(analytics.revenue.change)}`}>
                  {formatPercentage(analytics.revenue.change)} from last period
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.orders.current.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(analytics.orders.change)}
                <span className={`ml-1 ${getChangeColor(analytics.orders.change)}`}>
                  {formatPercentage(analytics.orders.change)} from last period
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.customers.current}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(analytics.customers.change)}
                <span className={`ml-1 ${getChangeColor(analytics.customers.change)}`}>
                  {formatPercentage(analytics.customers.change)} from last period
                </span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Websites</CardTitle>
              <Globe className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.websites.current}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {getChangeIcon(analytics.websites.change)}
                <span className={`ml-1 ${getChangeColor(analytics.websites.change)}`}>
                  {formatPercentage(analytics.websites.change)} from last period
                </span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Performing Websites */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Top Performing Websites
              </CardTitle>
              <CardDescription>Revenue and order performance by website</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.topWebsites.map((website, index) => (
                  <div key={website.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">#{index + 1}</span>
                        <span className="font-medium">{website.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{formatCurrency(website.revenue)}</div>
                        <div className="text-sm text-gray-500">{website.orders} orders</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <Progress 
                        value={(website.revenue / analytics.topWebsites[0].revenue) * 100} 
                        className="flex-1 mr-4" 
                      />
                      <div className={`text-sm ${getChangeColor(website.growth)}`}>
                        {formatPercentage(website.growth)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Performance</CardTitle>
              <CardDescription>Daily revenue and order trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.recentMetrics.map((metric, index) => (
                  <div key={metric.date} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-firefly-red rounded-full"></div>
                      <span className="font-medium">{metric.date}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(metric.revenue)}</div>
                      <div className="text-sm text-gray-500">{metric.orders} orders</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Average Order Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-firefly-red">
                {formatCurrency(analytics.revenue.current / analytics.orders.current)}
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Based on {analytics.orders.current.toLocaleString()} total orders
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Revenue per Website</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-firefly-red">
                {formatCurrency(analytics.revenue.current / analytics.websites.current)}
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Across {analytics.websites.current} active websites
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Customer Lifetime Value</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-firefly-red">
                {formatCurrency(analytics.revenue.current / analytics.customers.current)}
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Based on {analytics.customers.current} active customers
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
