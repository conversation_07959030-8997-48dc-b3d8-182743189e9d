import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Tags,
  GripVertical,
  Save,
} from "lucide-react";

interface Category {
  id: string;
  name: string;
  description: string;
  itemCount: number;
  isActive: boolean;
  order: number;
  createdAt: string;
}

const MenuCategories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [newCategoryDescription, setNewCategoryDescription] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    // Mock data loading
    const loadCategories = async () => {
      await new Promise(resolve => setTimeout(resolve, 800));

      setCategories([
        {
          id: "CAT-001",
          name: "Beef Burgers",
          description: "Classic beef burgers with various toppings and sauces",
          itemCount: 8,
          isActive: true,
          order: 1,
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: "CAT-002",
          name: "Chicken Burgers",
          description: "Grilled and crispy chicken burger options",
          itemCount: 5,
          isActive: true,
          order: 2,
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: "CAT-003",
          name: "Veggie Burgers",
          description: "Plant-based and vegetarian burger alternatives",
          itemCount: 3,
          isActive: true,
          order: 3,
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: "CAT-004",
          name: "Loaded Fries",
          description: "Crispy fries with various toppings and sauces",
          itemCount: 6,
          isActive: true,
          order: 4,
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: "CAT-005",
          name: "Drinks & Shakes",
          description: "Beverages, milkshakes, and refreshing drinks",
          itemCount: 12,
          isActive: true,
          order: 5,
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: "CAT-006",
          name: "Sides",
          description: "Appetizers and side dishes to complement your meal",
          itemCount: 7,
          isActive: true,
          order: 6,
          createdAt: "2024-01-01T00:00:00Z",
        },
        {
          id: "CAT-007",
          name: "Desserts",
          description: "Sweet treats and desserts to end your meal",
          itemCount: 4,
          isActive: false,
          order: 7,
          createdAt: "2024-01-01T00:00:00Z",
        },
      ]);

      setIsLoading(false);
    };

    loadCategories();
  }, []);

  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      setError("Category name is required");
      return;
    }

    try {
      // Mock category creation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newCategory: Category = {
        id: `CAT-${Date.now()}`,
        name: newCategoryName,
        description: newCategoryDescription,
        itemCount: 0,
        isActive: true,
        order: categories.length + 1,
        createdAt: new Date().toISOString(),
      };

      setCategories(prev => [...prev, newCategory]);
      setNewCategoryName("");
      setNewCategoryDescription("");
      setIsAddDialogOpen(false);
      setError("");
    } catch (err) {
      setError("Failed to create category");
    }
  };

  const handleEditCategory = async () => {
    if (!editingCategory || !newCategoryName.trim()) {
      setError("Category name is required");
      return;
    }

    try {
      // Mock category update
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setCategories(prev => prev.map(cat => 
        cat.id === editingCategory.id 
          ? { ...cat, name: newCategoryName, description: newCategoryDescription }
          : cat
      ));

      setEditingCategory(null);
      setNewCategoryName("");
      setNewCategoryDescription("");
      setIsEditDialogOpen(false);
      setError("");
    } catch (err) {
      setError("Failed to update category");
    }
  };

  const handleDeleteCategory = async (categoryId: string) => {
    if (window.confirm("Are you sure you want to delete this category? This action cannot be undone.")) {
      try {
        // Mock category deletion
        await new Promise(resolve => setTimeout(resolve, 500));
        setCategories(prev => prev.filter(cat => cat.id !== categoryId));
      } catch (err) {
        setError("Failed to delete category");
      }
    }
  };

  const toggleCategoryStatus = async (categoryId: string) => {
    try {
      // Mock status toggle
      await new Promise(resolve => setTimeout(resolve, 300));
      setCategories(prev => prev.map(cat => 
        cat.id === categoryId ? { ...cat, isActive: !cat.isActive } : cat
      ));
    } catch (err) {
      setError("Failed to update category status");
    }
  };

  const openEditDialog = (category: Category) => {
    setEditingCategory(category);
    setNewCategoryName(category.name);
    setNewCategoryDescription(category.description);
    setIsEditDialogOpen(true);
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/dashboard/menu")}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Menu
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Menu Categories</h1>
              <p className="text-gray-600">Organize your menu items into categories</p>
            </div>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-firefly-red hover:bg-red-700">
                <Plus className="w-4 h-4 mr-2" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Category</DialogTitle>
                <DialogDescription>
                  Create a new category to organize your menu items
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="categoryName">Category Name</Label>
                  <Input
                    id="categoryName"
                    placeholder="e.g., Appetizers"
                    value={newCategoryName}
                    onChange={(e) => setNewCategoryName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="categoryDescription">Description</Label>
                  <Input
                    id="categoryDescription"
                    placeholder="Brief description of this category"
                    value={newCategoryDescription}
                    onChange={(e) => setNewCategoryDescription(e.target.value)}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddCategory} className="bg-firefly-red hover:bg-red-700">
                  <Save className="w-4 h-4 mr-2" />
                  Create Category
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {/* Categories List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Tags className="w-5 h-5 mr-2" />
              Categories ({categories.length})
            </CardTitle>
            <CardDescription>
              Drag and drop to reorder categories. Click to edit or manage items.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {categories.map((category) => (
                <div
                  key={category.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <GripVertical className="w-5 h-5 text-gray-400 cursor-move" />
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="font-medium">{category.name}</h3>
                        <Badge variant={category.isActive ? "default" : "secondary"}>
                          {category.isActive ? "Active" : "Inactive"}
                        </Badge>
                        <Badge variant="outline">
                          {category.itemCount} items
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => openEditDialog(category)}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Category
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => toggleCategoryStatus(category.id)}>
                        {category.isActive ? "Deactivate" : "Activate"}
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => handleDeleteCategory(category.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Category</DialogTitle>
              <DialogDescription>
                Update the category name and description
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <div className="space-y-2">
                <Label htmlFor="editCategoryName">Category Name</Label>
                <Input
                  id="editCategoryName"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editCategoryDescription">Description</Label>
                <Input
                  id="editCategoryDescription"
                  value={newCategoryDescription}
                  onChange={(e) => setNewCategoryDescription(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEditCategory} className="bg-firefly-red hover:bg-red-700">
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Empty State */}
        {categories.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <Tags className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
            <p className="text-gray-600 mb-6">
              Create your first category to start organizing your menu items
            </p>
            <Button 
              onClick={() => setIsAddDialogOpen(true)}
              className="bg-firefly-red hover:bg-red-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Category
            </Button>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default MenuCategories;
