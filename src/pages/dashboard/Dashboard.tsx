import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import {
  UtensilsCrossed,
  ShoppingCart,
  TrendingUp,
  Users,
  DollarSign,
  Eye,
  Plus,
  ArrowUpRight,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

interface DashboardStats {
  totalMenuItems: number;
  totalOrders: number;
  totalRevenue: number;
  activeUsers: number;
  monthlyGrowth: number;
}

interface RecentOrder {
  id: string;
  customerName: string;
  website: string;
  amount: number;
  status: "pending" | "completed" | "cancelled";
  date: string;
}

interface MenuCategory {
  id: string;
  name: string;
  itemCount: number;
  status: "active" | "inactive";
  orders: number;
  revenue: number;
}

const Dashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalWebsites: 0,
    totalOrders: 0,
    totalRevenue: 0,
    activeUsers: 0,
    monthlyGrowth: 0,
  });
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [menuCategories, setMenuCategories] = useState<MenuCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock data loading
    const loadDashboardData = async () => {
      await new Promise(resolve => setTimeout(resolve, 1000));

      setStats({
        totalMenuItems: 42,
        totalOrders: 1847,
        totalRevenue: 45230,
        activeUsers: 324,
        monthlyGrowth: 12.5,
      });

      setRecentOrders([
        {
          id: "ORD-001",
          customerName: "John Smith",
          website: "Downtown Burgers",
          amount: 45.99,
          status: "completed",
          date: "2024-01-15T10:30:00Z",
        },
        {
          id: "ORD-002",
          customerName: "Sarah Johnson",
          website: "Gourmet Bites",
          amount: 32.50,
          status: "pending",
          date: "2024-01-15T09:15:00Z",
        },
        {
          id: "ORD-003",
          customerName: "Mike Wilson",
          website: "Quick Eats",
          amount: 28.75,
          status: "completed",
          date: "2024-01-15T08:45:00Z",
        },
        {
          id: "ORD-004",
          customerName: "Emily Davis",
          website: "Burger Palace",
          amount: 67.20,
          status: "cancelled",
          date: "2024-01-15T07:20:00Z",
        },
      ]);

      setMenuCategories([
        {
          id: "CAT-001",
          name: "Beef Burgers",
          itemCount: 8,
          status: "active",
          orders: 456,
          revenue: 18450,
        },
        {
          id: "CAT-002",
          name: "Chicken Burgers",
          itemCount: 5,
          status: "active",
          orders: 298,
          revenue: 12870,
        },
        {
          id: "CAT-003",
          name: "Loaded Fries",
          itemCount: 6,
          status: "active",
          orders: 234,
          revenue: 8650,
        },
        {
          id: "CAT-004",
          name: "Drinks & Shakes",
          itemCount: 12,
          status: "active",
          orders: 567,
          revenue: 5260,
        },
      ]);

      setIsLoading(false);
    };

    loadDashboardData();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "pending":
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case "cancelled":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "bg-green-100 text-green-800",
      inactive: "bg-gray-100 text-gray-800",
      maintenance: "bg-yellow-100 text-yellow-800",
      completed: "bg-green-100 text-green-800",
      pending: "bg-yellow-100 text-yellow-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={variants[status as keyof typeof variants] || variants.inactive}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-96 bg-gray-200 rounded"></div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back! Here's what's happening with your restaurant.</p>
          </div>
          <Link to="/dashboard/menu/new">
            <Button className="bg-firefly-red hover:bg-red-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Menu Item
            </Button>
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Menu Items</CardTitle>
              <UtensilsCrossed className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalMenuItems}</div>
              <p className="text-xs text-muted-foreground">
                +5 from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalOrders.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +{stats.monthlyGrowth}% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${stats.totalRevenue.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                +8.2% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeUsers}</div>
              <p className="text-xs text-muted-foreground">
                +12 from yesterday
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>Latest orders from all your websites</CardDescription>
              </div>
              <Link to="/dashboard/orders">
                <Button variant="outline" size="sm">
                  View All
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(order.status)}
                      <div>
                        <p className="font-medium">{order.customerName}</p>
                        <p className="text-sm text-gray-600">{order.website}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${order.amount}</p>
                      {getStatusBadge(order.status)}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Menu Performance */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Menu Performance</CardTitle>
                <CardDescription>Performance overview of your menu categories</CardDescription>
              </div>
              <Link to="/dashboard/menu">
                <Button variant="outline" size="sm">
                  Manage
                  <ArrowUpRight className="w-4 h-4 ml-1" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {menuCategories.map((category) => (
                  <div key={category.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{category.name}</p>
                        <p className="text-sm text-gray-600">{category.itemCount} items</p>
                      </div>
                      {getStatusBadge(category.status)}
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>{category.orders} orders</span>
                      <span className="font-medium">${category.revenue.toLocaleString()}</span>
                    </div>
                    <Progress value={(category.orders / 600) * 100} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks to help you manage your business</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link to="/dashboard/menu/new">
                <Button variant="outline" className="w-full justify-start h-auto p-4">
                  <Plus className="w-5 h-5 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Add Menu Item</div>
                    <div className="text-sm text-gray-600">Create a new menu item</div>
                  </div>
                </Button>
              </Link>
              
              <Link to="/dashboard/analytics">
                <Button variant="outline" className="w-full justify-start h-auto p-4">
                  <TrendingUp className="w-5 h-5 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">View Analytics</div>
                    <div className="text-sm text-gray-600">Check performance metrics</div>
                  </div>
                </Button>
              </Link>
              
              <Link to="/dashboard/orders">
                <Button variant="outline" className="w-full justify-start h-auto p-4">
                  <Eye className="w-5 h-5 mr-3" />
                  <div className="text-left">
                    <div className="font-medium">Monitor Orders</div>
                    <div className="text-sm text-gray-600">Track all incoming orders</div>
                  </div>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
