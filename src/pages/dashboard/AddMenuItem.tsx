import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  ArrowLeft,
  Save,
  Upload,
  DollarSign,
  Clock,
  UtensilsCrossed,
  Star,
  AlertTriangle,
} from "lucide-react";

const menuItemSchema = z.object({
  name: z.string().min(2, "Item name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  price: z.number().min(0.01, "Price must be greater than 0"),
  category: z.string().min(1, "Please select a category"),
  preparationTime: z.number().min(1, "Preparation time must be at least 1 minute"),
  status: z.string().min(1, "Please select a status"),
  isPopular: z.boolean().optional(),
  allergens: z.array(z.string()).optional(),
  image: z.string().optional(),
});

type MenuItemFormData = z.infer<typeof menuItemSchema>;

const AddMenuItem = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedAllergens, setSelectedAllergens] = useState<string[]>([]);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<MenuItemFormData>({
    resolver: zodResolver(menuItemSchema),
    defaultValues: {
      isPopular: false,
      allergens: [],
      status: "available",
    },
  });

  const category = watch("category");
  const status = watch("status");
  const isPopular = watch("isPopular");

  const categories = [
    "Beef Burgers",
    "Chicken Burgers", 
    "Veggie Burgers",
    "Loaded Fries",
    "Drinks & Shakes",
    "Sides",
    "Desserts",
    "Appetizers",
  ];

  const allergenOptions = [
    "gluten",
    "dairy", 
    "eggs",
    "nuts",
    "soy",
    "shellfish",
    "fish",
    "sesame",
  ];

  const onSubmit = async (data: MenuItemFormData) => {
    setIsLoading(true);
    setError("");

    try {
      // Mock menu item creation - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Add selected allergens to form data
      const formData = {
        ...data,
        allergens: selectedAllergens,
      };
      
      console.log("Creating menu item:", formData);
      
      // Redirect to menu page
      navigate("/dashboard/menu");
    } catch (err) {
      setError("Failed to create menu item. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAllergenChange = (allergen: string, checked: boolean) => {
    if (checked) {
      setSelectedAllergens(prev => [...prev, allergen]);
    } else {
      setSelectedAllergens(prev => prev.filter(a => a !== allergen));
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/dashboard/menu")}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Menu
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add Menu Item</h1>
            <p className="text-gray-600">Create a new item for your restaurant menu</p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UtensilsCrossed className="w-5 h-5 mr-2" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Enter the basic details about your menu item
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Item Name *</Label>
                  <Input
                    id="name"
                    placeholder="e.g., Classic Firefly Burger"
                    {...register("name")}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select onValueChange={(value) => setValue("category", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(cat => (
                        <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && (
                    <p className="text-sm text-red-600">{errors.category.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the ingredients, preparation, and what makes this item special..."
                  rows={3}
                  {...register("description")}
                />
                {errors.description && (
                  <p className="text-sm text-red-600">{errors.description.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">Price *</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      min="0"
                      placeholder="12.99"
                      className="pl-10"
                      {...register("price", { valueAsNumber: true })}
                    />
                  </div>
                  {errors.price && (
                    <p className="text-sm text-red-600">{errors.price.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="preparationTime">Prep Time (minutes) *</Label>
                  <div className="relative">
                    <Clock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="preparationTime"
                      type="number"
                      min="1"
                      placeholder="15"
                      className="pl-10"
                      {...register("preparationTime", { valueAsNumber: true })}
                    />
                  </div>
                  {errors.preparationTime && (
                    <p className="text-sm text-red-600">{errors.preparationTime.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Status *</Label>
                  <Select onValueChange={(value) => setValue("status", value)} defaultValue="available">
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="unavailable">Unavailable</SelectItem>
                      <SelectItem value="seasonal">Seasonal</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.status && (
                    <p className="text-sm text-red-600">{errors.status.message}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="isPopular"
                  checked={isPopular}
                  onCheckedChange={(checked) => setValue("isPopular", checked as boolean)}
                />
                <Label htmlFor="isPopular" className="flex items-center">
                  <Star className="w-4 h-4 mr-1 text-yellow-500" />
                  Mark as Popular Item
                </Label>
              </div>
            </CardContent>
          </Card>

          {/* Allergen Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Allergen Information
              </CardTitle>
              <CardDescription>
                Select any allergens present in this menu item
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {allergenOptions.map(allergen => (
                  <div key={allergen} className="flex items-center space-x-2">
                    <Checkbox 
                      id={allergen}
                      checked={selectedAllergens.includes(allergen)}
                      onCheckedChange={(checked) => handleAllergenChange(allergen, checked as boolean)}
                    />
                    <Label htmlFor={allergen} className="capitalize">
                      {allergen}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Image Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="w-5 h-5 mr-2" />
                Item Image
              </CardTitle>
              <CardDescription>
                Upload an appetizing photo of your menu item
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Upload menu item image
                </p>
                <p className="text-sm text-gray-600 mb-4">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500 mb-4">
                  PNG, JPG or WebP (max. 5MB)
                </p>
                <Button type="button" variant="outline">
                  Choose File
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate("/dashboard/menu")}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-firefly-red hover:bg-red-700"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? "Creating Item..." : "Create Menu Item"}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default AddMenuItem;
