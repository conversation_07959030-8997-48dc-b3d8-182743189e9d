import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import {
  Globe,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Eye,
  Trash2,
  ExternalLink,
  TrendingUp,
  Users,
  ShoppingCart,
} from "lucide-react";

interface Website {
  id: string;
  name: string;
  domain: string;
  status: "active" | "inactive" | "maintenance";
  orders: number;
  revenue: number;
  visitors: number;
  lastUpdated: string;
  description: string;
  image: string;
}

const Websites = () => {
  const [websites, setWebsites] = useState<Website[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock data loading
    const loadWebsites = async () => {
      await new Promise(resolve => setTimeout(resolve, 800));

      setWebsites([
        {
          id: "WEB-001",
          name: "Downtown Burgers",
          domain: "downtownburgers.com",
          status: "active",
          orders: 234,
          revenue: 12450,
          visitors: 1250,
          lastUpdated: "2024-01-15T10:30:00Z",
          description: "Premium burger restaurant in downtown area",
          image: "/burger.png",
        },
        {
          id: "WEB-002",
          name: "Gourmet Bites",
          domain: "gourmetbites.com",
          status: "active",
          orders: 189,
          revenue: 9870,
          visitors: 980,
          lastUpdated: "2024-01-14T15:20:00Z",
          description: "Artisanal burgers with gourmet ingredients",
          image: "/burger-02.png",
        },
        {
          id: "WEB-003",
          name: "Quick Eats",
          domain: "quickeats.com",
          status: "maintenance",
          orders: 156,
          revenue: 7650,
          visitors: 750,
          lastUpdated: "2024-01-13T09:15:00Z",
          description: "Fast casual dining with quick service",
          image: "/burger-top.png",
        },
        {
          id: "WEB-004",
          name: "Burger Palace",
          domain: "burgerpalace.com",
          status: "active",
          orders: 298,
          revenue: 15670,
          visitors: 1580,
          lastUpdated: "2024-01-15T14:45:00Z",
          description: "Luxury burger experience with premium ingredients",
          image: "/burger.png",
        },
        {
          id: "WEB-005",
          name: "Street Food Co",
          domain: "streetfoodco.com",
          status: "inactive",
          orders: 67,
          revenue: 3420,
          visitors: 340,
          lastUpdated: "2024-01-10T11:30:00Z",
          description: "Street-style burgers and sides",
          image: "/burger-02.png",
        },
        {
          id: "WEB-006",
          name: "Healthy Bites",
          domain: "healthybites.com",
          status: "active",
          orders: 145,
          revenue: 8920,
          visitors: 890,
          lastUpdated: "2024-01-15T08:20:00Z",
          description: "Healthy and organic burger options",
          image: "/burger-top.png",
        },
      ]);

      setIsLoading(false);
    };

    loadWebsites();
  }, []);

  const filteredWebsites = websites.filter(website =>
    website.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    website.domain.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const variants = {
      active: "bg-green-100 text-green-800",
      inactive: "bg-gray-100 text-gray-800",
      maintenance: "bg-yellow-100 text-yellow-800",
    };

    return (
      <Badge className={variants[status as keyof typeof variants] || variants.inactive}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Websites</h1>
            <p className="text-gray-600">Manage all your restaurant websites in one place</p>
          </div>
          <Link to="/dashboard/websites/new">
            <Button className="bg-firefly-red hover:bg-red-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Website
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search websites..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Websites Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWebsites.map((website) => (
            <Card key={website.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <img 
                      src={website.image} 
                      alt={website.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                    <div>
                      <CardTitle className="text-lg">{website.name}</CardTitle>
                      <CardDescription className="flex items-center">
                        <Globe className="w-3 h-3 mr-1" />
                        {website.domain}
                      </CardDescription>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link to={`/dashboard/websites/${website.id}`}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to={`/dashboard/websites/${website.id}/edit`}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Visit Site
                      </DropdownMenuItem>
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="flex items-center justify-between mt-2">
                  {getStatusBadge(website.status)}
                  <span className="text-sm text-gray-500">
                    Updated {formatDate(website.lastUpdated)}
                  </span>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">{website.description}</p>
                
                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 pt-2 border-t">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <ShoppingCart className="w-4 h-4 text-firefly-red" />
                    </div>
                    <div className="text-lg font-semibold">{website.orders}</div>
                    <div className="text-xs text-gray-500">Orders</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <TrendingUp className="w-4 h-4 text-green-500" />
                    </div>
                    <div className="text-lg font-semibold">${website.revenue.toLocaleString()}</div>
                    <div className="text-xs text-gray-500">Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Users className="w-4 h-4 text-blue-500" />
                    </div>
                    <div className="text-lg font-semibold">{website.visitors}</div>
                    <div className="text-xs text-gray-500">Visitors</div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-2 pt-2">
                  <Link to={`/dashboard/websites/${website.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="w-4 h-4 mr-2" />
                      View
                    </Button>
                  </Link>
                  <Link to={`/dashboard/websites/${website.id}/edit`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Edit className="w-4 h-4 mr-2" />
                      Edit
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredWebsites.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <Globe className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? "No websites found" : "No websites yet"}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm 
                ? "Try adjusting your search terms" 
                : "Get started by creating your first restaurant website"
              }
            </p>
            {!searchTerm && (
              <Link to="/dashboard/websites/new">
                <Button className="bg-firefly-red hover:bg-red-700">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Your First Website
                </Button>
              </Link>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Websites;
