export interface InstagramPost {
  id: string;
  imageUrl: string;
  likes: number;
  link: string;
}

export const getInstagramPosts = (): InstagramPost[] => {
  // These are the actual image names in the public/instagram folder
  const imageNames = [
    "instagram-01.jpg",
    "instagram-02.jpg",
    "instagram-03.jpg",
    "instagram-04.jpg",
    "instagram-05.jpg",
    "instagram-06.jpg",
    "instagram-07.jpg",
    "instagram-08.jpg"
  ];
  
  // Create posts from the image names
  return imageNames.map((imageName, index) => ({
    id: String(index + 1),
    imageUrl: `/instagram/${imageName}`,
    likes: Math.floor(Math.random() * 200) + 50, // Random number of likes between 50-250
    link: "https://www.instagram.com/fireflyburgerjo/"
  }));
}; 