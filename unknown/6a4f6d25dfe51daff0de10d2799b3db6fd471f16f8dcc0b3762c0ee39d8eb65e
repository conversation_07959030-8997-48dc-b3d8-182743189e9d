
import { Facebook, Instagram, Video, MapPin } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

const Footer = () => {
  return (
    <footer id="contact" className="bg-firefly-black pt-16 pb-8 text-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
          {/* Brand Section */}
          <div className="space-y-6">
            <div className="flex items-center">
              <h2 className="text-2xl font-bold">
                Fire<span className="text-firefly-red">Fly</span> Burger
              </h2>
            </div>
            <p className="text-gray-400">
              Premium gourmet burgers crafted to ignite your taste buds and satisfy your cravings.
            </p>
            <div className="flex space-x-4">
              <a 
                href="#" 
                className="bg-gray-800 hover:bg-firefly-red p-2 rounded-full transition-colors duration-300"
                aria-label="Instagram"
              >
                <Instagram size={20} />
              </a>
              <a 
                href="#" 
                className="bg-gray-800 hover:bg-firefly-red p-2 rounded-full transition-colors duration-300"
                aria-label="Facebook"
              >
                <Facebook size={20} />
              </a>
              <a 
                href="#" 
                className="bg-gray-800 hover:bg-firefly-red p-2 rounded-full transition-colors duration-300"
                aria-label="TikTok"
              >
                <Video size={20} />
              </a>
            </div>
          </div>

          {/* Links Section */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <a href="#" className="text-gray-400 hover:text-firefly-red transition-colors">Home</a>
              </li>
              <li>
                <a href="#menu" className="text-gray-400 hover:text-firefly-red transition-colors">Menu</a>
              </li>
              <li>
                <a href="#locations" className="text-gray-400 hover:text-firefly-red transition-colors">Locations</a>
              </li>
              <li>
                <a href="#" className="text-gray-400 hover:text-firefly-red transition-colors">About Us</a>
              </li>
              <li>
                <a href="#contact" className="text-gray-400 hover:text-firefly-red transition-colors">Contact</a>
              </li>
            </ul>
          </div>

          {/* Hours Section */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold">Operating Hours</h3>
            <ul className="space-y-3">
              <li className="text-gray-400">Monday - Thursday: 11AM - 10PM</li>
              <li className="text-gray-400">Friday - Saturday: 11AM - 11PM</li>
              <li className="text-gray-400">Sunday: 12PM - 9PM</li>
            </ul>
            
            <div className="pt-2">
              <h3 className="text-xl font-bold">Location</h3>
              <address className="text-gray-400 not-italic mt-3 flex items-start">
                <MapPin size={18} className="mr-2 mt-1 text-firefly-red" />
                <div>
                  123 Flame Street<br />
                  Burgertown, BT 12345
                </div>
              </address>
            </div>
          </div>

          {/* Newsletter Section */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold">Stay Updated</h3>
            <p className="text-gray-400">
              Subscribe to get special offers, free giveaways, and new menu announcements.
            </p>
            <div className="mt-4 flex flex-col sm:flex-row gap-2">
              <Input 
                type="email" 
                placeholder="Your email address"
                className="bg-gray-800 border-gray-700 text-white"
              />
              <Button className="bg-firefly-red hover:bg-red-700">
                Subscribe
              </Button>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-10 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm">
              &copy; {new Date().getFullYear()} FireFly Burger. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-gray-500 hover:text-gray-300 text-sm">Privacy Policy</a>
              <a href="#" className="text-gray-500 hover:text-gray-300 text-sm">Terms of Service</a>
              <a href="#" className="text-gray-500 hover:text-gray-300 text-sm">Accessibility</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
