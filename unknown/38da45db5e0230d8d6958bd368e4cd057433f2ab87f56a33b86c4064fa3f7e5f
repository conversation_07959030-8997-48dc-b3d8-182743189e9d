
import React from 'react';
import { cn } from '@/lib/utils';
import { Button as ShadcnButton } from '@/components/ui/button';
import type { ButtonProps } from '@/components/ui/button';

interface GlowButtonProps extends ButtonProps {
  glowColor?: 'red' | 'white' | 'none';
  className?: string;
}

export const GlowButton: React.FC<GlowButtonProps> = ({
  glowColor = 'red',
  className,
  children,
  ...props
}) => {
  const glowClasses = {
    red: 'hover:shadow-[0_0_15px_rgba(234,56,76,0.6)] transition-shadow duration-300',
    white: 'hover:shadow-[0_0_15px_rgba(255,255,255,0.4)] transition-shadow duration-300',
    none: '',
  };

  return (
    <ShadcnButton
      className={cn(glowClasses[glowColor], className)}
      {...props}
    >
      {children}
    </ShadcnButton>
  );
};
