import { useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { Instagram } from "lucide-react";
import { getInstagramPosts } from "@/lib/instagramService";

const InstagramFeed = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" });
  const instagramPosts = getInstagramPosts();

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  return (
    <section className="py-16 bg-firefly-darkgray">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="hero-title text-3xl md:text-4xl text-white mb-4">
            FOLLOW US ON <span className="text-firefly-red">INSTAGRAM</span>
          </h2>
          <div className="flex items-center justify-center gap-2 mb-6">
            <Instagram className="text-white" size={24} />
            <a 
              href="https://www.instagram.com/fireflyburgerjo/" 
              target="_blank" 
              rel="noopener noreferrer" 
              className="text-white hover:text-firefly-red transition-colors"
            >
              @fireflyburgerjo
            </a>
          </div>
        </div>

        <div className="relative">
          {/* Carousel Navigation */}
          <button 
            onClick={scrollPrev}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-black/60 text-white p-3 rounded-full hover:bg-firefly-red transition-colors"
            aria-label="Previous"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M15 18l-6-6 6-6" />
            </svg>
          </button>
          
          <button 
            onClick={scrollNext}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-black/60 text-white p-3 rounded-full hover:bg-firefly-red transition-colors"
            aria-label="Next"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 18l6-6-6-6" />
            </svg>
          </button>
          
          {/* Embla Carousel */}
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex gap-4">
              {instagramPosts.map((post) => (
                <div key={post.id} className="w-52 h-52 flex-shrink-0">
                  <a 
                    href={post.link} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="block group relative"
                  >
                    <div className="relative aspect-square overflow-hidden rounded-lg">
                      <img 
                        src={post.imageUrl} 
                        alt="Instagram post" 
                        className="w-52 h-52 object-cover transition-transform duration-500 group-hover:scale-110" 
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                        <div className="flex items-center text-white">
                          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                          </svg>
                          {post.likes}
                        </div>
                      </div>
                    </div>
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-10 text-center">
          <a 
            href="https://www.instagram.com/fireflyburgerjo/" 
            target="_blank" 
            rel="noopener noreferrer" 
            className="inline-flex items-center gap-2 bg-firefly-red hover:bg-red-700 text-white px-8 py-3 rounded-md transition-colors"
          >
            <Instagram size={20} />
            View Instagram
          </a>
        </div>
      </div>
    </section>
  );
};

export default InstagramFeed; 